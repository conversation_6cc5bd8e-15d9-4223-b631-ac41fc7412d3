"use client";

import React, { useState, useEffect, Suspense, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { Users, CheckCircle, XCircle, Info, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import AuthHeader from '@/components/Navigation/AuthHeader';

// Import the shared fallback component
import ClientFallback from '@/components/ClientFallback';

// Main component that uses searchParams
function TrusteeInvitationContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [invitation, setInvitation] = useState<any>(null);
  const [inviter, setInviter] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [isDeclining, setIsDeclining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [acceptTerms, setAcceptTerms] = useState(false);

  const fetchInvitation = useCallback(async (inviteId: string) => {
    try {
      setIsLoading(true);
      console.log('Fetching invitation with ID:', inviteId);

      // Use the API endpoint to fetch the invitation
      const url = `/api/trustees/invitation?id=${inviteId}`;

      console.log('Fetching from URL:', url);
      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch invitation');
      }

      const data = await response.json();
      const { invitation, inviter, currentUserId } = data;

      console.log('Invitation data received:', {
        invitationId: invitation?.id,
        inviterName: inviter?.first_name,
        currentUserId,
        currentUser: user?.id,
        userLoggedIn: !!user
      });

      // If the user is logged in but the UI shows they're not, reload the page
      // This can happen due to cookie/session issues
      if (user && currentUserId && user.id !== currentUserId) {
        console.warn('User ID mismatch between client and server. Reloading page.');
        window.location.reload();
        return;
      }

      if (!invitation) {
        setError('Invitation not found');
        return;
      }

      if (invitation.status === 'revoked') {
        setError('This invitation has been revoked');
        return;
      }

      if (invitation.status === 'active') {
        setError('This invitation has already been accepted');
        return;
      }

      setInvitation(invitation);
      setInviter(inviter);
    } catch (error: any) {
      console.error('Error fetching invitation:', error);
      setError('Failed to load invitation details');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    const inviteId = searchParams.get('id');

    if (!inviteId) {
      setError('Invalid invitation link');
      setIsLoading(false);
      return;
    }

    fetchInvitation(inviteId);
  }, [searchParams, fetchInvitation]);

  // Rest of the file remains exactly the same...
  // [Previous content from line 107 to the end of file]
  // Function to link a trustee to a user account
  const linkTrusteeToUser = async (trusteeId: string, userId: string) => {
    try {
      // Use API route to update trustee record with admin privileges
      const response = await fetch('/api/trustees/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trusteeId,
          trustee_user_id: userId,
          status: 'active',
          invitation_accepted_at: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to link trustee to user account');
      }

      toast.success('You are now a trustee!');
      return true;
    } catch (error: any) {
      console.error('Error linking trustee to user:', error);
      toast.error(error.message || 'Failed to link trustee to user account');
      return false;
    }
  };

  const handleAcceptInvitation = async () => {
    try {
      if (!acceptTerms) {
        toast.error('Please accept the trustee responsibilities before continuing');
        return;
      }

      setIsAccepting(true);

      // Store the invitation details in localStorage regardless of auth state
      // This ensures we don't lose context during the auth flow
      localStorage.setItem('pendingTrusteeId', invitation.id);
      localStorage.setItem('pendingTrusteeEmail', invitation.trustee_email);
      localStorage.setItem('pendingTrusteeAcceptedAt', new Date().toISOString());
      localStorage.setItem('trusteeTermsAccepted', 'true');

      console.log('Stored trustee invitation in localStorage:', invitation.id, 'with terms accepted');

      // If the user is logged in, link the trustee to their account immediately
      if (user) {
        console.log('User is logged in, linking trustee to account:', user.id);

        // Check if the user's email matches the invitation email
        if (user.email && invitation.trustee_email &&
            user.email.toLowerCase() !== invitation.trustee_email.toLowerCase()) {
          console.warn(`Email mismatch: Invitation for ${invitation.trustee_email}, but user logged in with ${user.email}`);
          toast.warning('This invitation was sent to a different email address. You can continue, but the invitation will be linked to your current account.');
        }

        const linked = await linkTrusteeToUser(invitation.id, user.id);
        if (!linked) return;

        // Redirect to dashboard after successful linking
        toast.success('Invitation accepted successfully');
        router.push('/dashboard');
        return;
      } else {
        // If the user is not logged in, mark the invitation as pending
        // and redirect to authentication
        console.log('User not logged in, marking invitation as pending');

        // Use API route to update trustee status with admin privileges
        const response = await fetch('/api/trustees/update-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            trusteeId: invitation.id,
            status: 'pending_auth',
            invitation_accepted_at: new Date().toISOString(),
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update trustee status');
        }

        // Check if the email matches an existing account in both tables
        const { data: existingCustomUser } = await supabase
          .from('custom_users')
          .select('id, email')
          .eq('email', invitation.trustee_email.toLowerCase())
          .maybeSingle();

        const { data: existingProfile } = await supabase
          .from('profiles')
          .select('id, email')
          .eq('email', invitation.trustee_email.toLowerCase())
          .maybeSingle();

        const existingUser = existingCustomUser || existingProfile;

        if (existingUser) {
          console.log('Existing user found with matching email, redirecting to login');
          // User exists, redirect to login with clear instructions
          toast.success('Please sign in to complete your trustee invitation');
          router.push(`/login?redirect=/dashboard&trusteeId=${invitation.id}`);
        } else {
          console.log('No existing user found, redirecting to register');
          // User doesn't exist, redirect to register with pre-filled email
          toast.success('Please create an account to complete your trustee invitation');
          router.push(`/register?redirect=/dashboard&trusteeId=${invitation.id}&email=${encodeURIComponent(invitation.trustee_email)}&trustee=${invitation.id}`);
        }
        return;
      }
    } catch (error: any) {
      console.error('Error accepting invitation:', error);
      toast.error(error.message || 'Failed to accept invitation');
    } finally {
      setIsAccepting(false);
    }
  };

  const handleDeclineInvitation = async () => {
    try {
      setIsDeclining(true);

      // Use API route to update trustee status with admin privileges
      const response = await fetch('/api/trustees/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trusteeId: invitation.id,
          status: 'revoked',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to decline invitation');
      }

      toast.success('Invitation declined');
      router.push('/');
    } catch (error: any) {
      console.error('Error declining invitation:', error);
      toast.error(error.message || 'Failed to decline invitation');
    } finally {
      setIsDeclining(false);
    }
  };

  const renderPermissions = (permissions: string[]) => {
    const permissionLabels: Record<string, string> = {
      assets: 'Digital and physical assets',
      vault: 'Digital vault documents',
      contacts: 'Emergency contacts',
      services: 'Services to cancel',
    };

    return (
      <ul className="list-disc pl-5 space-y-1">
        {permissions.map((permission) => (
          <li key={permission}>
            {permissionLabels[permission] || permission}
          </li>
        ))}
      </ul>
    );
  };

  // Always use AuthHeader for the accept page to ensure consistent UI
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <AuthHeader />
      <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md">
          {isLoading ? (
            <div className="text-center">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-500">Loading invitation...</p>
            </div>
          ) : error ? (
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Invitation Error</CardTitle>
                <CardDescription>
                  {error}
                </CardDescription>
              </CardHeader>
              <CardFooter className="flex justify-center">
                <Button asChild>
                  <Link href="/">
                    Return to Home
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ) : invitation && inviter ? (
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Trustee Invitation</CardTitle>
                <CardDescription>
                  You've been invited to be a trustee
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center mb-4">
                  <p className="text-lg font-medium">
                    {inviter.first_name} {inviter.last_name}
                  </p>
                  <p className="text-sm text-gray-500">
                    {inviter.email}
                  </p>
                </div>

                <div className="bg-blue-50 p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                    <p className="text-sm text-blue-800">
                      <strong>{inviter.first_name}</strong> has invited you to be their trustee. As a trustee, you will be responsible for managing their digital legacy after their passing.
                    </p>
                  </div>
                  <div className="text-sm text-blue-800 mt-2">
                    <p>Being a trustee is an important responsibility. You will only gain access to the designated information after verification of {inviter.first_name}'s passing.</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">You will have access to:</p>
                  {renderPermissions(invitation.permissions)}
                </div>

                <div className="pt-4 space-y-4">
                  <div className="bg-amber-50 p-4 rounded-md">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-600 mr-2 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-amber-800">Important Information</h4>
                        <p className="text-sm text-amber-700 mt-1">
                          As a trustee, you will be responsible for managing aspects of {inviter.first_name}'s digital legacy. This may include closing accounts, transferring assets, and ensuring their final wishes are respected.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={acceptTerms}
                      onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
                    />
                    <label
                      htmlFor="terms"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      I understand and accept my responsibilities as a trustee for {inviter.first_name} {inviter.last_name}
                    </label>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-3">
                <Button
                  className="w-full"
                  onClick={handleAcceptInvitation}
                  disabled={isAccepting || !acceptTerms}
                >
                  {isAccepting ? (
                    <>
                      <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Accept Invitation
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleDeclineInvitation}
                  disabled={isDeclining}
                >
                  {isDeclining ? 'Declining...' : 'Decline'}
                </Button>
              </CardFooter>
            </Card>
          ) : null}
        </div>
      </div>
    </div>
  );
}

// Export the page component with Suspense
export default function AcceptTrusteeInvitationPage() {
  return (
    <Suspense fallback={<ClientFallback />}>
      <TrusteeInvitationContent />
    </Suspense>
  );
}
