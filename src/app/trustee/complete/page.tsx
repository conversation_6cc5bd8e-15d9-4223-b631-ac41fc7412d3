"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/lib/supabase';
import { Check<PERSON>ircle, XCircle, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import MainHeader from '@/components/Navigation/MainHeader';
import AuthHeader from '@/components/Navigation/AuthHeader';

type TrusteeData = {
  id: string;
  trustee_user_id: string | null;
  status: string;
  trustee_email: string;
  user_id: string;
};

export default function TrusteeCompletePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const completeTrusteeInvitation = useCallback(async (trusteeId: string) => {
    if (!user?.id) {
      setError('User not authenticated');
      return;
    }

    try {
      setIsProcessing(true);

      const { data: trusteeData, error: fetchError } = await supabase
        .from('trustees')
        .select('*')
        .eq('id', trusteeId)
        .single();

      if (fetchError || !trusteeData) {
        throw new Error(fetchError?.message || 'Trustee record not found');
      }

      if (trusteeData.trustee_user_id && trusteeData.trustee_user_id !== user.id) {
        throw new Error('This invitation has already been accepted by another user');
      }

      // Use API route to update trustee record with admin privileges
      const response = await fetch('/api/trustees/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trusteeId,
          trustee_user_id: user.id,
          status: 'active',
          invitation_accepted_at: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to complete trustee invitation');
      }

      // Clean up local storage
      ['pendingTrusteeId', 'pendingTrusteeEmail', 'pendingTrusteeAcceptedAt'].forEach(key => 
        localStorage.removeItem(key)
      );

      setIsComplete(true);
      toast.success('Trustee invitation successfully accepted');
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : 'Failed to complete trustee invitation';
      setError(message);
      toast.error(message);
    } finally {
      setIsProcessing(false);
    }
  }, [user]);

  useEffect(() => {
    if (loading) return;

    if (!user) {
      router.push('/login?redirect=/trustee/complete');
      return;
    }

    const trusteeId = searchParams.get('trusteeId') || localStorage.getItem('pendingTrusteeId');
    if (!trusteeId) {
      setError('No pending trustee invitation found');
      return;
    }

    const trusteeEmail = localStorage.getItem('pendingTrusteeEmail');
    if (trusteeEmail && user.email && trusteeEmail.toLowerCase() !== user.email.toLowerCase()) {
      toast.warning('Email does not match invitation, proceeding anyway');
    }

    completeTrusteeInvitation(trusteeId);
  }, [user, loading, searchParams, completeTrusteeInvitation, router]);

  if (loading || isProcessing) {
    return (
      <div className="flex flex-col min-h-screen bg-gray-50">
        {user ? <MainHeader /> : <AuthHeader />}
        <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="mx-auto w-full max-w-md text-center">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4" />
            <p className="text-gray-500">Processing trustee invitation...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {user ? <MainHeader /> : <AuthHeader />}
      <div className="flex flex-1 flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md">
          {error ? (
            <ErrorCard error={error} />
          ) : isComplete ? (
            <SuccessCard />
          ) : null}
        </div>
      </div>
    </div>
  );
}

function ErrorCard({ error }: { error: string }) {
  return (
    <Card className="text-center">
      <CardHeader>
        <div className="mx-auto w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
          <XCircle className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle>Error</CardTitle>
        <CardDescription>{error}</CardDescription>
      </CardHeader>
      <CardFooter className="flex justify-center">
        <Button asChild>
          <Link href="/">Return to Home</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

function SuccessCard() {
  return (
    <Card className="text-center">
      <CardHeader>
        <div className="mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
          <CheckCircle className="h-6 w-6 text-green-600" />
        </div>
        <CardTitle>Invitation Accepted</CardTitle>
        <CardDescription>
          You have successfully accepted the trustee invitation.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-500 mb-4">
          As a trustee, you will be responsible for managing digital assets and documents.
        </p>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button asChild>
          <Link href="/dashboard">
            Go to Dashboard <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
