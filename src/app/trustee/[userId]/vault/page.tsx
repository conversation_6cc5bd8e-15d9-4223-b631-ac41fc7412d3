"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/context/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, FileText, Download, Eye, Lock, Shield } from 'lucide-react';
import PageHeading from '@/components/ui/PageHeading';
import { decryptFile, base64ToArrayBuffer } from '@/utils/encryption';
import { toast } from 'sonner';

interface UserDocument {
  id: string;
  name: string;
  category: string;
  file_path: string;
  encryption_key: string;
  file_type: string;
  file_size: number;
  is_encrypted: boolean;
  description?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  file_name?: string;
}

export default function TrusteeVaultPage() {
  const { userId } = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [documents, setDocuments] = useState<UserDocument[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);
  const [hasAccess, setHasAccess] = useState(false);
  const [userName, setUserName] = useState('');

  const documentCategories = [
    { value: 'all', label: 'All Documents' },
    { value: 'will', label: 'Will' },
    { value: 'trust', label: 'Trust' },
    { value: 'financial', label: 'Financial' },
    { value: 'medical', label: 'Medical' },
    { value: 'insurance', label: 'Insurance' },
    { value: 'property', label: 'Property' },
    { value: 'digital', label: 'Digital' },
    { value: 'other', label: 'Other' },
  ];

  const fetchUserInfo = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('first_name, last_name')
        .eq('id', userId)
        .single();

      if (error) throw error;
      if (data) setUserName(`${data.first_name} ${data.last_name}`);
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  }, [userId]);

  const checkAccess = useCallback(async () => {
    try {
      const { data: trusteeData, error: trusteeError } = await supabase
        .from('trustees')
        .select('*')
        .eq('user_id', userId)
        .eq('trustee_user_id', user?.id)
        .eq('status', 'active')
        .single();

      if (trusteeError || !trusteeData) {
        toast.error('You do not have access to this user\'s documents');
        router.push('/trustee/dashboard');
        return;
      }

      if (!trusteeData.permissions.includes('vault')) {
        toast.error('You do not have permission to view this user\'s documents');
        router.push('/trustee/dashboard');
        return;
      }

      const { data: deathData, error: deathError } = await supabase
        .from('death_notifications')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'verified')
        .single();

      if (deathError || !deathData) {
        toast.error('You cannot access this information until the user\'s death has been verified');
        router.push('/trustee/dashboard');
        return;
      }

      setHasAccess(true);
      fetchUserInfo();
    } catch (error: any) {
      console.error('Error checking access:', error);
      toast.error('Failed to verify access permissions');
      router.push('/trustee/dashboard');
    }
  }, [user, userId, router, fetchUserInfo]);

  const fetchDocuments = useCallback(async () => {
    try {
      setIsLoading(true);
      let query = supabase
        .from('vault_documents')
        .select('*')
        .eq('user_id', userId);

      if (searchQuery) query = query.ilike('name', `%${searchQuery}%`);
      if (selectedCategory && selectedCategory !== 'all') query = query.eq('category', selectedCategory);

      const { data, error } = await query.order('updated_at', { ascending: false });

      if (error) {
        if (error.code === '42P01') {
          setDocuments([]);
          toast.error('Vault feature is not fully set up. Please contact support.');
          return;
        }
        throw error;
      }

      setDocuments(data || []);
    } catch (error: any) {
      console.error('Error fetching documents:', error);
      toast.error(`Failed to load documents: ${error.message}`);
      setDocuments([]);
    } finally {
      setIsLoading(false);
    }
  }, [userId, searchQuery, selectedCategory]);

  useEffect(() => {
    if (user) checkAccess();
  }, [user, userId, checkAccess]);

  useEffect(() => {
    if (hasAccess) fetchDocuments();
  }, [hasAccess, fetchDocuments]);

  const handleViewDocument = async (document: UserDocument) => {
    try {
      setIsProcessing(document.id);
      const { data, error } = await supabase.storage
        .from('documents')
        .download(document.file_path);

      if (error || !data) throw error || new Error('No data returned');

      const originalFileName = document.file_path.split('/').pop() || 'document';
      const decryptedFile = await decryptFile(data, document.encryption_key, originalFileName, document.file_type);
      
      const blobUrl = URL.createObjectURL(decryptedFile);
      window.open(blobUrl, '_blank');
    } catch (error: any) {
      console.error('Error viewing document:', error);
      toast.error(`Failed to decrypt document: ${error.message}`);
    } finally {
      setIsProcessing(null);
    }
  };

  const handleDownloadDocument = async (document: UserDocument) => {
    try {
      setIsProcessing(document.id);
      const { data, error } = await supabase.storage
        .from('documents')
        .download(document.file_path);

      if (error) throw error;

      const originalPath = document.file_path.split('/').pop() || '';
      const originalName = originalPath.split('_').slice(1).join('_');
      const fileName = document.name;
      const fileExtension = originalName.split('.').pop();
      const fullFileName = `${fileName}${fileExtension ? '.' + fileExtension : ''}`;

      const decryptedFile = await decryptFile(data, document.encryption_key, fullFileName, document.file_type);
      const blobUrl = URL.createObjectURL(decryptedFile);
      
      const downloadLink = window.document.createElement('a');
      downloadLink.href = blobUrl;
      downloadLink.download = fullFileName;
      window.document.body.appendChild(downloadLink);
      downloadLink.click();
      window.document.body.removeChild(downloadLink);
      
      setTimeout(() => URL.revokeObjectURL(blobUrl), 100);
    } catch (error: any) {
      console.error('Error downloading document:', error);
      toast.error(`Failed to decrypt document: ${error.message}`);
    } finally {
      setIsProcessing(null);
    }
  };

  if (!hasAccess) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center py-16">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-500">Verifying access...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" className="mb-6" onClick={() => router.push('/trustee/dashboard')}>
        Back to Dashboard
      </Button>
      
      <PageHeading
        title={`${userName}'s Digital Vault`}
        description="Access important documents that have been shared with you as a trustee."
      />

      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="relative w-full md:w-1/2">
          <Input
            type="text"
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pr-10"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
        </div>

        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            {documentCategories.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="text-center py-10">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-500">Loading documents...</p>
        </div>
      ) : documents.length === 0 ? (
        <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
          <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
          <p className="text-gray-500 mb-6 max-w-md mx-auto">
            There are no documents in this vault that have been shared with you.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {documents.map((document) => (
            <Card key={document.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-lg">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  <span className="truncate">{document.name}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-4">
                  <span className="text-sm text-gray-500 mr-2">Category:</span>
                  <span className="text-sm font-medium bg-gray-100 px-2 py-0.5 rounded">
                    {document.category}
                  </span>
                </div>

                <div className="flex items-center text-xs text-gray-500 mb-4">
                  <Lock className="h-3 w-3 mr-1 text-green-500" />
                  <span>End-to-end encrypted</span>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDocument(document)}
                    disabled={isProcessing === document.id}
                  >
                    {isProcessing === document.id ? (
                      <>
                        <div className="animate-spin h-4 w-4 mr-2 border-2 border-primary border-t-transparent rounded-full"></div>
                        Decrypting...
                      </>
                    ) : (
                      <>
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadDocument(document)}
                    disabled={isProcessing === document.id}
                  >
                    {isProcessing === document.id ? (
                      <>
                        <div className="animate-spin h-4 w-4 mr-2 border-2 border-primary border-t-transparent rounded-full"></div>
                        Decrypting...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
