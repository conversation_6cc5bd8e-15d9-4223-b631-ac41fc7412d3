// Test script to verify encryption/decryption functionality
const fs = require('fs');
const path = require('path');

// Simple test to verify the encryption logic
async function testEncryption() {
  console.log('Testing encryption/decryption logic...');
  
  // Create a test file
  const testContent = 'This is a test document content for encryption testing.';
  const testFile = new File([testContent], 'test.txt', { type: 'text/plain' });
  
  try {
    // Import the encryption functions
    const { encryptFile, decryptFile, arrayBufferToBase64 } = require('./src/utils/encryption.ts');
    
    console.log('Original file size:', testFile.size);
    
    // Encrypt the file
    const { encryptedFile, encryptionKey } = await encryptFile(testFile);
    console.log('Encrypted file size:', encryptedFile.size);
    
    // Convert key to base64 for storage
    const keyBase64 = arrayBufferToBase64(encryptionKey);
    console.log('Encryption key length (base64):', keyBase64.length);
    
    // Decrypt the file
    const decryptedFile = await decryptFile(encryptedFile, keyBase64, 'test.txt', 'text/plain');
    console.log('Decrypted file size:', decryptedFile.size);
    
    // Read the decrypted content
    const decryptedContent = await decryptedFile.text();
    console.log('Decrypted content:', decryptedContent);
    
    // Verify the content matches
    if (decryptedContent === testContent) {
      console.log('✅ Encryption/decryption test PASSED');
    } else {
      console.log('❌ Encryption/decryption test FAILED');
      console.log('Expected:', testContent);
      console.log('Got:', decryptedContent);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testEncryption();
